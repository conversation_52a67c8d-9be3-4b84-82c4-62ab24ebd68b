import React from 'react';
import en from '~/localization/en';
import colors from '~/styles/colors';
import {
  StopType,
  TaskListItem,
  StopStatusType,
  StopWithRelations,
} from '~/types/stops.types';
import {
  ArrowDown,
  ArrowUp,
  ArrowUpDown,
  Service as ServiceIcon,
} from '~/components/icons';
import { Parcel } from '~/types/parcel.types';
import { Service } from '~/types/service.types';
import { DateTime } from 'luxon';
export type StopTypeInfo = {
  title: string;
  icon: React.ReactNode;
  color: string;
};

export const StopTypeInfo: Record<StopType, StopTypeInfo> = {
  [StopType.Delivery]: {
    title: en.delivery,
    icon: <ArrowDown color={colors.darkBlue500} />,
    color: colors.darkBlue500,
  },
  [StopType.Pickup]: {
    title: en.pickup,
    icon: <ArrowUp color={colors.darkBlue500} />,
    color: colors.darkBlue500,
  },
  [StopType.Service]: {
    title: en.service,
    icon: <ServiceIcon color={colors.darkBlue500} />,
    color: colors.darkBlue500,
  },
  [StopType.Exchange]: {
    title: en.exchange,
    icon: <ArrowUpDown color={colors.darkBlue500} />,
    color: colors.darkBlue500,
  },
  [StopType.Meet]: {
    title: '',
    icon: undefined,
    color: colors.darkBlue500,
  },
  [StopType.Flight]: {
    title: '',
    icon: undefined,
    color: colors.darkBlue500,
  },
  [StopType.Start]: {
    title: en.pickup,
    icon: <ArrowUp color={colors.darkBlue500} />,
    color: colors.darkBlue500,
  },
  [StopType.End]: {
    title: en.delivery,
    icon: <ArrowDown color={colors.darkBlue500} />,
    color: colors.darkBlue500,
  },
  [StopType.Break]: {
    title: '',
    icon: undefined,
    color: colors.darkBlue500,
  },
};

export const getStopStepperPillColors = (
  status: StopStatusType,
): [string, string] => {
  if (!status) {
    return [colors.blue50, colors.darkBlue500];
  }

  switch (status) {
    case 'ARRIVED':
      return [colors.blue50, colors.darkBlue500];
    case 'SCHEDULED':
      return [colors.backgroundLight, colors.darkGray];
    case 'COMPLETE':
      return [colors.greenLight, colors.greenDark];
    case 'ON_SCHEDULE':
      return [colors.yellowLight, colors.yellowDark];
    case 'OPEN':
      return [colors.yellowLight, colors.yellowDark];
    case 'LATE':
      return [colors.yellowLight, colors.yellowDark];
    case 'INACTIVE':
      return [colors.red25, colors.red600];
    default:
      return [colors.blue50, colors.darkBlue500];
  }
};

const compareStrings = (
  stopType: StopType,
  serviceType: string | null,
): boolean => {
  if (!serviceType) return false;

  const processedStr1 = serviceType.replace(/\s+/g, '').toLowerCase();
  const processedStr2 = stopType.toLowerCase();

  return processedStr1 === processedStr2;
};

export const getServiceType = (
  serviceType: string | null,
): StopType | undefined => {
  if (compareStrings(StopType.Pickup, serviceType)) {
    return StopType.Pickup;
  }

  if (compareStrings(StopType.Delivery, serviceType)) {
    return StopType.Delivery;
  }

  if (compareStrings(StopType.Service, serviceType)) {
    return StopType.Service;
  }

  return undefined;
};

export const getCoordinates = (
  stop: StopWithRelations,
): { latitude: number; longitude: number } | null => {
  let latitude = Number(stop?.Stop_Coordinates__Latitude__s);
  let longitude = Number(stop?.Stop_Coordinates__Longitude__s);

  if (!latitude || !longitude) {
    const coordinates = stop?.Coordinates__c;

    if (coordinates) {
      const [latStr, lngStr] = coordinates.split(',');
      latitude = Number(latStr);
      longitude = Number(lngStr);

      // Validate that both latitude and longitude are valid numbers and not empty strings
      if (latitude && longitude && !isNaN(latitude) && !isNaN(longitude)) {
        return { latitude, longitude };
      }
    }

    return null;
  }

  return { latitude, longitude };
};

const getIconForStopType = (stopType: StopType) => {
  switch (stopType) {
    case StopType.Pickup:
      return <ArrowUp color={colors.darkBlue500} />;
    case StopType.Delivery:
      return <ArrowDown color={colors.darkBlue500} />;
    case StopType.Service:
      return <ServiceIcon color={colors.darkBlue500} />;
    default:
      return <ServiceIcon color={colors.darkBlue500} />;
  }
};

export const getAddressString = (stop: StopWithRelations) => {
  let address = '';

  if (stop?.Address__c) {
    return stop?.Address__c;
  } else if (stop?.Address_1__c) {
    address = stop?.Address_1__c;

    if (stop?.Address_2__c) {
      address += ', ' + stop?.Address_2__c;
    }

    if (stop?.City__c) {
      address += ', ' + stop?.City__c;
    }

    if (stop?.State__c) {
      address += ', ' + stop?.State__c;
    }

    if (stop?.Postal_Code__c) {
      address += ', ' + stop?.Postal_Code__c;
    }
  }

  return address;
};

const cloneToPlainObject = <T,>(source: T): T =>
  JSON.parse(JSON.stringify(source));

const createServiceInfo = (
  service: Service,
  type: StopType | string,
): TaskListItem => ({
  id: service.Id,
  type,
  data: cloneToPlainObject(service),
  icon: getIconForStopType(type ?? StopType.Service),
});

export const buildStopTasks = ({
  isPickup,
  isDelivery,
  isService,
  pickupParcels,
  deliveryParcels,
  stopServices,
}: {
  isPickup: boolean;
  isDelivery: boolean;
  isService: boolean;
  pickupParcels: Parcel[];
  deliveryParcels: Parcel[];
  stopServices: Service[];
}): TaskListItem[] => {
  const items: TaskListItem[] = [];

  if (isPickup || (!isService && !isDelivery)) {
    items.push({
      id: 'pickup',
      type: StopType.Pickup,
      data: Array.from(pickupParcels, parcel => cloneToPlainObject(parcel)),
      icon: getIconForStopType(StopType.Pickup),
    });
  }

  if (isDelivery) {
    items.push({
      id: 'delivery',
      type: StopType.Delivery,
      data: Array.from(deliveryParcels, parcel => cloneToPlainObject(parcel)),
      icon: getIconForStopType(StopType.Delivery),
    });
  }

  if (isService) {
    for (const service of stopServices) {
      const serviceType =
        getServiceType(service.Service_Type_Name__c) ?? StopType.Service;

      if (serviceType === StopType.Pickup && !isPickup) {
        items.push(createServiceInfo(service, StopType.Pickup));
      } else {
        items.push(createServiceInfo(service, serviceType));
      }
      // else if (serviceType === StopType.Delivery && !isDelivery) {
      //   items.push(createServiceInfo(service, StopType.Delivery));
      // }
    }
  }

  return items;
};

export const isValidDate = (dateString: string): boolean => {
  const date = DateTime.fromISO(dateString);
  return date.isValid;
};

export const isValidStop = (stop: any): stop is StopWithRelations => {
  return (
    stop && typeof stop === 'object' && stop.Id && typeof stop.Id === 'string'
  );
};

export const sortStopsByTime = (
  stops: StopWithRelations[],
): StopWithRelations[] => {
  return [...stops].sort((a, b) => {
    const timeA = a.Stop_Time_Preferred__c || '';
    const timeB = b.Stop_Time_Preferred__c || '';
    return timeA.localeCompare(timeB);
  });
};
