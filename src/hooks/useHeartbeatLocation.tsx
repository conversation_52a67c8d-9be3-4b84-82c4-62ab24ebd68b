import { useEffect, useCallback } from 'react';
import { getStopsByDate } from '~/db/realm/operations/stop.operations';
import { StopWithRelations } from '~/types/stops.types';
import {
  getDailyScheduleIdByDate,
  getRouteById,
  saveDataToRealm,
} from '~/db/realm';
import {
  setRouteAndStopId,
  startHeartbeat,
  stopHeartbeat,
} from '~/services/location/LocationService';

type UseHeartbeatLocationProps = {
  date?: string;
};

export const useHeartbeatLocation = ({ date }: UseHeartbeatLocationProps) => {
  /**
   * Fetches the next stop ID for a given date.
   */
  const getNextStop = useCallback(
    async (selectedDate: string): Promise<StopWithRelations | null> => {
      try {
        const stopList: StopWithRelations[] =
          await getStopsByDate(selectedDate);

        if (!stopList || stopList.length === 0) {
          console.info('No stops found for the given date:', selectedDate);
          return null;
        }

        // Perform sorting in a separate statement
        const sortedStops = [...stopList].sort((a, b) => {
          const timeA = a.Stop_Time_Preferred__c || ''; // Handle null values safely
          const timeB = b.Stop_Time_Preferred__c || '';

          return timeA.localeCompare(timeB);
        });

        // Return the ID of the first stop from the sorted list
        const nextStop = sortedStops[0] || null;

        return nextStop;
      } catch (error) {
        console.error('useHeartbeatLocation: getNextStop(): ', error);
        return null;
      }
    },
    [],
  );

  /**
   * Handles daily schedules that are in progress.
   */
  const handleHeartbeatForInProgressSchedule = useCallback(
    async (selectedDate: string) => {
      const latestStop = await getNextStop(selectedDate);

      if (latestStop) {
        const latestStopId = latestStop?.Id;
        const routeId = latestStop?.Summary__c ?? undefined;
        const dailyScheduleId = latestStop?.Daily_Schedule__c ?? undefined;

        const isLockCheckInOrderEnabled: boolean =
          await checkForLockCheckInOrder(routeId);

        await saveDataToRealm<string | undefined>(
          'dailyScheduleId',
          dailyScheduleId,
        );
        await saveDataToRealm<boolean>(
          'lockCheckInOrder',
          isLockCheckInOrderEnabled,
        );

        setRouteAndStopId(
          routeId,
          latestStopId,
          dailyScheduleId,
          isLockCheckInOrderEnabled,
        );

        console.info(
          `[FINAL] Going to start location tracking for Route id: ${routeId}, Stop id: ${latestStopId}, daily schedule id: ${dailyScheduleId} for post date ${selectedDate}`,
        );
        startHeartbeat();

        await saveDataToRealm<string | null>('routeId', routeId ?? null);
        await saveDataToRealm<string | null>('stopId', latestStopId ?? null);
        await saveDataToRealm<string>('date', selectedDate);
      } else {
        const dailyScheduleId = await getDailyScheduleIdByDate(selectedDate);

        if (dailyScheduleId) {
          await saveDataToRealm<string>('dailyScheduleId', dailyScheduleId);
          setRouteAndStopId(undefined, undefined, dailyScheduleId, false);

          console.info(
            `[FINAL] Going to start location tracking for Daily Schedule id: ${dailyScheduleId} for post date ${selectedDate}`,
          );
          startHeartbeat();
        } else {
          console.info(
            '[FINAL] No daily schedule found for location tracking. Stopping heartbeat.',
          );
          stopHeartbeat();
        }
      }
    },
    [getNextStop],
  );

  const checkForLockCheckInOrder = async (routeId: string | null) => {
    if (!routeId) {
      return false;
    }

    try {
      const route = await getRouteById(routeId);
      return route && !route.Lock_Check_In_Order__c;
    } catch (error) {
      console.warn(
        `[HEARTBEAT] Failed to get route ${routeId}, defaulting to regular mode:`,
        error,
      );
      return false;
    }
  };

  const handleTaskChange = useCallback(async () => {
    if (!date) return;

    await handleHeartbeatForInProgressSchedule(date);
  }, [date, handleHeartbeatForInProgressSchedule]);

  useEffect(() => {
    console.info('Date for location tracking: ', date);

    if (!date) return;

    const debounceTimer = setTimeout(() => {
      handleTaskChange();
    }, 5000); // Debounce duration: 5 seconds

    // Cleanup the timer if the dependency changes before 5 seconds
    return () => {
      clearTimeout(debounceTimer);
    };
  }, [handleTaskChange, date]);

  return { handleTaskChange };
};
