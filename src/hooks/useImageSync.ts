import { useCallback, useEffect, useRef, useState } from 'react';
import { useIsOnline } from '~/hooks/useIsOnline';
import { getRealmInstance } from '~/db/realm/utils/realm.config';
import { ImageSchema } from '~/db/realm/schemas/image.schema';
import { IImage } from '~/types/image.types';
import { API_ENDPOINT_KEYS } from '~/api/apiEndpoints';
import { postData } from '~/api/apiService';
import { InteractionManager } from 'react-native';
import { safeWrite } from '~/db/realm/utils/safeRealm';
import { useQuery } from '@realm/react';
import Realm from 'realm';
import Config from 'react-native-config';

const IMAGE_BATCH_SIZE = 3;
const MAX_RETRY_COUNT = 4;
const NETWORK_ID = Config.NETWORK_ID;

export const IMAGE_SYNC_STATUS = {
  IDLE: 'idle',
  SYNCING: 'syncing',
  ERROR: 'error',
  SUCCESS: 'success',
} as const;

type ImageSyncStatus =
  (typeof IMAGE_SYNC_STATUS)[keyof typeof IMAGE_SYNC_STATUS];

export const useImageSync = () => {
  const statusRef = useRef<ImageSyncStatus>(IMAGE_SYNC_STATUS.IDLE);
  const [imageSyncRequests, setImageSyncRequests] = useState<IImage[]>([]);

  const pendingImages = useQuery<IImage>({
    type: ImageSchema.name,
    query: records => {
      return records.filtered(
        `retryCount < ${MAX_RETRY_COUNT} && isSynced == false && isSyncReady == true`,
      );
    },
  });

  const shouldCheckForSync = pendingImages.length !== imageSyncRequests.length;
  const { isOnline } = useIsOnline();

  // Fetch unsynced images from Realm
  const fetchImageSyncRequests = useCallback(async () => {
    let realm: Realm | null = null;

    try {
      realm = await getRealmInstance();
      if (!realm || realm.isClosed) return null;

      const results = realm
        .objects<IImage>(ImageSchema.name)
        .filtered(
          `retryCount < ${MAX_RETRY_COUNT} && isSynced == false && isSyncReady == true`,
        );
      setImageSyncRequests(prevRequests => {
        const newRequests = Array.from(results);
        if (
          prevRequests.length === newRequests.length &&
          prevRequests.every((req, idx) => req.Id === newRequests[idx].Id)
        ) {
          return prevRequests;
        }
        return newRequests;
      });
    } catch (error) {
      console.error('useImageSync: fetchImageSyncRequests():', error);
    }
  }, []);

  useEffect(() => {
    if (isOnline && shouldCheckForSync) fetchImageSyncRequests();
  }, [isOnline, shouldCheckForSync, fetchImageSyncRequests]);

  const handleSyncResponse = useCallback(
    async (request: IImage, response: any, error: any) => {
      try {
        const realm = await getRealmInstance();
        if (!realm || realm.isClosed) return null;

        safeWrite(realm, () => {
          const image = realm.objectForPrimaryKey<IImage>(
            ImageSchema.name,
            request.Id,
          );
          if (image) {
            image.isSynced = !error;
            image.updatedAt = new Date();
            if (error) image.retryCount = image.retryCount + 1;
          }
        });

        if (error) {
          console.error(
            `Image Upload Error: ${request.Id}`,
            JSON.stringify(error),
          );
        } else {
          console.info(
            `Image Upload Success: ${request.Id}`,
            JSON.stringify(response),
          );
        }
      } catch (syncErrorerror) {
        console.error('useImageSync: handleSyncResponse():', syncErrorerror);
      }
    },
    [],
  );

  const syncImageBatch = useCallback(
    async (requests: IImage[]) => {
      try {
        const uploadPromises = requests.map(async request => {
          return postData({
            onSync: (response, error) =>
              handleSyncResponse(request, response, error),
            requestIdentifier: API_ENDPOINT_KEYS.POST_CONTENT_VERSION,
            requestData: {
              Title: request.Title,
              VersionData: request.VersionData,
              PathOnClient: request.PathOnClient,
              NetworkId: NETWORK_ID,
              Linked_Entity_Id__c: [
                request.RouteSummaryId,
                request.StopId,
                request.ParcelId,
              ]
                .filter(Boolean)
                .join(','),
              Coordinates__Latitude__s: request.Coordinates__Latitude__s,
              Coordinates__Longitude__s: request.Coordinates__Longitude__s,
            },
          });
        });
        await Promise.allSettled(uploadPromises);
        return true;
      } catch (error) {
        console.error('useImageSync: syncImageBatch():', error);
        return false;
      }
    },
    [handleSyncResponse],
  );

  const performImageSync = useCallback(() => {
    InteractionManager.runAfterInteractions(async () => {
      if (statusRef.current === IMAGE_SYNC_STATUS.SYNCING) return;
      statusRef.current = IMAGE_SYNC_STATUS.SYNCING;

      try {
        const requests = imageSyncRequests;
        for (let i = 0; i < requests.length; i += IMAGE_BATCH_SIZE) {
          const batch = requests.slice(i, i + IMAGE_BATCH_SIZE);
          await syncImageBatch(batch);
        }
        statusRef.current = IMAGE_SYNC_STATUS.SUCCESS;
        // Re-fetch images in case there are still some left (after sync, e.g., on error)
        fetchImageSyncRequests();
      } catch (error) {
        console.error('useImageSync: performImageSync():', error);
        statusRef.current = IMAGE_SYNC_STATUS.ERROR;
      }
    });
  }, [imageSyncRequests, syncImageBatch, fetchImageSyncRequests]);

  // Automatically sync on changes or online
  useEffect(() => {
    if (!isOnline || imageSyncRequests.length === 0) return;
    performImageSync();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOnline, imageSyncRequests.length]);

  return {
    status: statusRef.current,
    syncImages: performImageSync,
  };
};
