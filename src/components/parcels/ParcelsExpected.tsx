import {
  Text,
  TextStyle,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
  ViewStyle,
  StyleSheet,
} from 'react-native';
import { buttonText, h4, h5 } from '~/styles/text';
import colors from '~/styles/colors';
import {
  marginBottom8,
  marginTop12,
  marginTop8,
  verticalSpace12,
} from '~/styles/spacing';
import React from 'react';
import { ParcelWithBackground } from '~/assets/icons';
import en from '~/localization/en';
import { row, center } from '~/styles/views';
import ParcelRow from '~/components/parcels/ParcelRow';
import { Parcel } from '~/types/parcel.types';
import { StopType } from '~/types/stops.types';
import { ParcelManager } from '~/services/sync/parcelManager';
import { getRealmInstance } from '~/db/realm/utils/realm.config';
import { ImageSchema } from '~/db/realm/schemas/image.schema';
import { safeWrite } from '~/db/realm/utils/safeRealm';
import CustomModal from '~/components/modals/CustomModal';
import { BarCodeFilled } from '~/components/icons';
import { Divider } from '@rneui/base';

interface ParcelsExpectedProps {
  isLastStop: boolean;
  stopType: StopType;
  deliveryParcels: Parcel[];
  pickupParcels: Parcel[];
  navigateToAddParcelFlow: () => void;
  handleEditParcel: (parcel: Parcel) => void;
  onParcelDeleted: () => void;
  openBarcodeScanner: (targetedParcelId?: string) => void;
  isBarcodeMismatchModalVisible: boolean;
  onDismissBarcodeModal: () => void;
}

const ParcelsExpected: React.FC<ParcelsExpectedProps> = ({
  stopType,
  deliveryParcels,
  pickupParcels,
  navigateToAddParcelFlow,
  handleEditParcel,
  onParcelDeleted,
  openBarcodeScanner,
  isBarcodeMismatchModalVisible,
  onDismissBarcodeModal,
}) => {
  const isPickup = stopType === StopType.Pickup || stopType === StopType.Start;
  const isDelivery =
    stopType === StopType.Delivery || stopType === StopType.End;

  const handleParcelDelete = async (parcelId: string) => {
    try {
      const result = await ParcelManager.deleteParcel(parcelId);
      if (!result.success) return;
      const realm = await getRealmInstance();

      if (!realm || realm.isClosed) return;

      safeWrite(realm, () => {
        const imagesToDelete = realm
          .objects(ImageSchema.name)
          .filtered('ParcelId == $0', parcelId);
        realm.delete(imagesToDelete);
      });

      onParcelDeleted?.();
    } catch (error) {
      console.error('Failed to delete parcel or associated images:', error);
    }
  };

  const renderParcels = (parcels: Parcel[]) => {
    const pickupParcelsToBeScanned = (parcel: Parcel) =>
      parcel.Reference__c !== null &&
      (isPickup
        ? parcel.Pickup_Scan_Time__c === null && parcel.Work_Order__c !== null
        : parcel.Delivery_Scan_Time__c === null && parcel.Pickup__c !== '');

    const parcelsNotToBeScanned = (parcel: Parcel) =>
      isPickup
        ? parcel.Reference__c === null ||
          parcel.Pickup_Scan_Time__c !== null ||
          parcel.Work_Order__c === null
        : parcel.Delivery_Scan_Time__c !== null ||
          parcel.Pickup__c === '' ||
          parcel.Reference__c === null;

    const parcelsToBeScanned = parcels.filter(pickupParcelsToBeScanned);
    const parcelsWithoutReference = parcels.filter(parcelsNotToBeScanned);

    const hasReference = parcelsToBeScanned.length > 0;
    const canBeEdited = (parcel: Parcel) =>
      (isPickup && parcel.Work_Order__c === null) ||
      (isDelivery && parcel.Pickup__c === '');

    const canBeDeleted = (parcel: Parcel) =>
      (isPickup && parcel.Work_Order__c === null) ||
      (isDelivery && parcel.Pickup__c === '' && parcel.Work_Order__c === null);

    const shouldShowCheckmark = (parcel: Parcel) =>
      (isPickup &&
        parcel.Work_Order__c !== null &&
        parcel.Pickup_Scan_Time__c !== null) ||
      (isDelivery &&
        parcel.Reference__c !== null &&
        parcel.Delivery_Scan_Time__c !== null);

    return (
      <>
        {parcelsWithoutReference.map(parcel => (
          <ParcelRow
            key={parcel.Id}
            parcel={parcel}
            isEditable={canBeEdited(parcel)}
            canBeDeleted={canBeDeleted(parcel)}
            shouldBeScanned={false}
            shouldShowCheckmark={shouldShowCheckmark(parcel)}
            onEditClick={() => handleEditParcel(parcel)}
            onDelete={() => handleParcelDelete(parcel.Id)}
          />
        ))}

        {hasReference && <Divider style={verticalSpace12} />}

        {hasReference && (
          <View style={marginTop12}>
            <View style={[styles.row, marginBottom8]}>
              <Text style={styles.infoLabel}>{en.pending_scan}</Text>
              <TouchableOpacity onPress={() => openBarcodeScanner()}>
                <Text style={styles.redTextButton}>{en.scan_barcode}</Text>
              </TouchableOpacity>
            </View>

            {parcelsToBeScanned.map(parcel => (
              <ParcelRow
                key={parcel.Id}
                parcel={parcel}
                isEditable={false}
                canBeDeleted={false}
                onEditClick={() => handleEditParcel(parcel)}
                onDelete={() => handleParcelDelete(parcel.Id)}
                shouldBeScanned={true}
                onScanPress={() => openBarcodeScanner(parcel.Id)}
              />
            ))}
          </View>
        )}

        {parcels.length === 0 && (
          <TouchableWithoutFeedback onPress={navigateToAddParcelFlow}>
            <View style={styles.parcelPlaceholder}>
              <ParcelWithBackground />
              <Text style={styles.infoText}>{en.add_parcels_manually}</Text>
            </View>
          </TouchableWithoutFeedback>
        )}
      </>
    );
  };

  return (
    <>
      {isPickup && (
        <View>
          <TitleRow
            title={en.pickup_parcels}
            shouldShowAddButton={true}
            onClick={navigateToAddParcelFlow}
          />
          {renderParcels(pickupParcels)}
        </View>
      )}

      {isDelivery && (
        <View>
          <TitleRow
            title={en.delivery_parcels}
            shouldShowAddButton={true}
            onClick={navigateToAddParcelFlow}
          />
          {renderParcels(deliveryParcels)}
        </View>
      )}

      <CustomModal
        isVisible={isBarcodeMismatchModalVisible}
        headerIcon={<BarCodeFilled />}
        headerText={en.barcode_not_matched}
        descriptionText={en.barcode_not_matched_description}
        cancelButtonText={en.cancel}
        onCancelPress={onDismissBarcodeModal}
        onClose={onDismissBarcodeModal}
        disabled={false}
      />
    </>
  );
};

const TitleRow = ({
  title,
  shouldShowAddButton,
  onClick,
}: {
  title: string;
  shouldShowAddButton: boolean;
  onClick: () => void;
}) => (
  <View style={[styles.row, marginBottom8]}>
    <Text style={styles.infoLabel}>{title}</Text>
    {shouldShowAddButton && (
      <TouchableOpacity style={marginTop8} onPress={onClick}>
        <Text style={styles.redTextButton}>{en.add_new}</Text>
      </TouchableOpacity>
    )}
  </View>
);

const styles = StyleSheet.create({
  infoLabel: {
    ...h5,
    color: colors.grey900,
  } as TextStyle,
  infoText: {
    ...h4,
    ...marginTop8,
    color: colors.grey900,
    fontSize: 16,
  } as TextStyle,
  row: {
    ...(row as ViewStyle),
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  redTextButton: {
    ...(buttonText as TextStyle),
    color: colors.red400,
  } as TextStyle,
  parcelPlaceholder: {
    ...(center as ViewStyle),
    height: 120,
    backgroundColor: colors.backgroundLight,
    borderRadius: 8,
  },
});

export default ParcelsExpected;
