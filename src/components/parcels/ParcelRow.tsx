import React, { useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Pen, Trash, BarCodeOutlined, Checkmark } from '~/components/icons';
import { Parcel } from '~/types/parcel.types';
import colors from '~/styles/colors';
import { blackText } from '~/styles/text';
import en from '~/localization/en';
import CustomModal from '~/components/modals/CustomModal';
import { DustbinWithBackground } from '~/assets/icons';

interface ParcelRowProps {
  parcel: Parcel;
  isEditable: boolean;
  onEditClick: () => void;
  onDelete: (parcelId: string) => void;
  canBeDeleted: boolean;
  shouldBeScanned: boolean;
  shouldShowCheckmark?: boolean;
  onScanPress?: () => void;
}

const ParcelRow = ({
  parcel,
  isEditable,
  onEditClick,
  onDelete,
  canBeDeleted,
  shouldBeScanned = false,
  shouldShowCheckmark = false,
  onScanPress,
}: ParcelRowProps) => {
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);

  const handleDelete = async () => {
    onDelete(parcel.Id);
    setIsDeleteModalVisible(false);
  };

  const checkmarkView = () => (
    <View style={styles.iconButton}>
      <Checkmark />
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.row}>
        <Text style={styles.text}>
          <Text style={styles.quantity}>{parcel.Quantity__c}</Text>{' '}
          {parcel.Parcel_Type_Name__c}
        </Text>

        {shouldBeScanned ? (
          onScanPress ? (
            <TouchableOpacity onPress={onScanPress} style={styles.iconButton}>
              <BarCodeOutlined />
            </TouchableOpacity>
          ) : (
            checkmarkView()
          )
        ) : (
          <>
            {shouldShowCheckmark && checkmarkView()}
            {isEditable && (
              <TouchableOpacity onPress={onEditClick} style={styles.iconButton}>
                <Pen />
              </TouchableOpacity>
            )}
            {canBeDeleted && (
              <TouchableOpacity
                onPress={() => setIsDeleteModalVisible(true)}
                style={styles.iconButton}>
                <Trash />
              </TouchableOpacity>
            )}
          </>
        )}
      </View>

      <CustomModal
        testID="delete-parcel-modal"
        isVisible={isDeleteModalVisible}
        headerIcon={<DustbinWithBackground />}
        onClose={() => setIsDeleteModalVisible(false)}
        headerText={en.delete_parcel
          .replace('{quantity}', String(parcel.Quantity__c ?? ''))
          .replace('{type}', parcel.Parcel_Type_Name__c ?? '')}
        descriptionText={en.delete_parcel_description}
        okButtonText={en.delete}
        cancelButtonText={en.cancel}
        onOkPress={handleDelete}
        onCancelPress={() => setIsDeleteModalVisible(false)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 10,
    backgroundColor: colors.lightGrayishBlue,
    borderRadius: 10,
    marginVertical: 4,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  text: {
    fontSize: 16,
    ...blackText,
    flex: 1,
  },
  quantity: {
    fontWeight: '500',
  },
  iconButton: {
    marginLeft: 12,
  },
});

export default ParcelRow;
