import React from 'react';
import BottomSheetModal, {
  ActionItem,
} from '~/components/modals/BottomSheetModal';
import { callPhoneNumber } from '~/services/dispatch/CallService';
import { sendSMS } from '~/services/dispatch/SmsService';

/**
 * The Contact Dispatch component opens up a modal with <PERSON><PERSON><PERSON><PERSON>'s phone number with call and sms functionality.
 *
 * @component
 * @param {boolean} props.isVisible - This is variable that maintains the state of modal whether it is open or close.
 * @param {function} props.onClose - This is the function that gets called when the user clicks on the cancel button or click anywhere in the background of the modal.

 * @example
 * // Example usage of ContactDispatch
 * <ContactDispatch
 *    isVisible={showContactOptionsDialog}
 *    onClose={close}
 * />
 */

const ContactDispatch = ({ isVisible, onClose }: any) => {
  const actions: ActionItem[] = [
    {
      id: 'sms',
      title: 'Send Message ****** 675 0477',
      onPress: () => sendSMS('****** 675 0477'),
    },
    {
      id: 'call',
      title: 'Call ****** 341 9640',
      onPress: () => callPhoneNumber('****** 341 9640'),
    },
  ];

  return (
    <BottomSheetModal
      isVisible={isVisible}
      onClose={onClose}
      variant="actionList"
      actions={actions}
    />
  );
};

export default ContactDispatch;
