import React from 'react';
import { View, StyleSheet, Text, TouchableOpacity, TextStyle, ViewStyle } from 'react-native';
import Modal from 'react-native-modal';
import colors from '~/styles/colors';
import { marginBottom10 } from '~/styles/spacing';
import { buttonText, centerText } from '~/styles/text';
import { center, deviceWidth } from '~/styles/views';

/**
 * Interface for action button items in the action list variant.
 */
export interface ActionItem {
  id: string;
  title: string;
  onPress: () => void;
  style?: 'default' | 'cancel';
}

/**
 * Interface for BottomSheetModal Props.
 */
interface BottomSheetModalProps {
  isVisible: boolean;
  onClose: () => void;
  children?: React.ReactNode;
  variant?: 'default' | 'actionList';
  actions?: ActionItem[];
}

/**
 * The BottomSheetModal component renders a modal at the bottom of the screen with customizable options.
 *
 * Supports two variants:
 * - 'default': Basic modal that renders children (backward compatible)
 * - 'actionList': Pre-styled modal with action buttons for common use cases
 *
 * @component
 * @param {boolean} isVisible - Whether the modal is visible or not. Default is false.
 * @param {function} props.onClose - A callback function that is triggered when the modal is closed. Default is () => {}.
 * @param {React.ReactNode} props.children - Content displayed in default variant. Optional.
 * @param {'default' | 'actionList'} props.variant - The variant of the modal. Default is 'default'.
 * @param {ActionItem[]} props.actions - Array of action items for actionList variant. Optional.
 */
const BottomSheetModal = ({
  isVisible = false,
  onClose = () => {},
  children,
  variant = 'default',
  actions = [],
}: BottomSheetModalProps) => {
  const renderActionButton = (action: ActionItem, index: number, isInGroup: boolean, isLast: boolean) => (
    <TouchableOpacity
      key={action.id}
      style={[
        styles.baseButton,
        isInGroup ? styles.groupedButton : styles.cancelButton,
        isInGroup && index === 0 && styles.firstButton,
        isInGroup && isLast && styles.lastButton,
      ]}
      onPress={action.onPress}>
      <Text style={[
        styles.baseButtonText,
        action.style === 'cancel' && styles.cancelButtonText
      ]}>
        {action.title}
      </Text>
    </TouchableOpacity>
  );

  const renderActionList = () => {
    const regularActions = actions.filter(action => action.style !== 'cancel');
    const cancelActions = actions.filter(action => action.style === 'cancel');

    return (
      <View style={styles.container}>
        {/* Grouped regular actions */}
        {regularActions.length > 0 && (
          <View style={styles.actionGroup}>
            {regularActions.map((action, index) => (
              <React.Fragment key={action.id}>
                {renderActionButton(action, index, true, index === regularActions.length - 1)}
                {index < regularActions.length - 1 && <View style={styles.divider} />}
              </React.Fragment>
            ))}
          </View>
        )}

        {/* Separated cancel actions */}
        {cancelActions.map((action, index) => (
          <View key={action.id} style={styles.cancelGroup}>
            {renderActionButton(action, index, false, false)}
          </View>
        ))}
      </View>
    );
  };

  const renderContent = () => {
    if (variant === 'actionList') {
      return renderActionList();
    }
    return children && <View>{children}</View>;
  };

  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={onClose}
      onSwipeComplete={onClose}
      swipeDirection="down"
      style={styles.modal}
      backdropOpacity={0.5}
      backdropTransitionOutTiming={0}
      statusBarTranslucent>
      {renderContent()}
    </Modal>
  );
};

// Constants for consistent sizing
const BUTTON_WIDTH = deviceWidth - 40;
const BUTTON_HEIGHT = 56;
const BORDER_RADIUS = 12;

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  container: {
    ...center,
    marginBottom: 20,
  } as ViewStyle,
  actionGroup: {
    width: BUTTON_WIDTH,
    backgroundColor: colors.grayishBlue,
    borderRadius: BORDER_RADIUS,
    overflow: 'hidden',
  } as ViewStyle,
  cancelGroup: {
    width: '100%',
    alignItems: 'center',
    marginTop: 8,
  } as ViewStyle,
  baseButton: {
    ...center,
    width: '100%',
    height: BUTTON_HEIGHT,
  } as ViewStyle,
  groupedButton: {
    backgroundColor: 'transparent',
  } as ViewStyle,
  cancelButton: {
    width: BUTTON_WIDTH,
    backgroundColor: colors.grayishBlue,
    borderRadius: BORDER_RADIUS,
    ...marginBottom10,
  } as ViewStyle,
  firstButton: {
    borderTopLeftRadius: BORDER_RADIUS,
    borderTopRightRadius: BORDER_RADIUS,
  } as ViewStyle,
  lastButton: {
    borderBottomLeftRadius: BORDER_RADIUS,
    borderBottomRightRadius: BORDER_RADIUS,
  } as ViewStyle,
  divider: {
    height: StyleSheet.hairlineWidth,
    backgroundColor: colors.buttonGray,
  } as ViewStyle,
  baseButtonText: {
    ...buttonText,
    ...centerText,
    color: colors.blue600,
  } as TextStyle,
  cancelButtonText: {
    color: colors.blue700,
    fontWeight: 'bold',
  } as TextStyle,
});

export default BottomSheetModal;
