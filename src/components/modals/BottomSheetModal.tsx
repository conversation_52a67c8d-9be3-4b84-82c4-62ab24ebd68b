import React from 'react';
import {
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  TextStyle,
  ViewStyle,
} from 'react-native';
import Modal from 'react-native-modal';
import colors from '~/styles/colors';
import { marginBottom10 } from '~/styles/spacing';
import { buttonText, centerText } from '~/styles/text';
import { center, deviceWidth } from '~/styles/views';

/**
 * Interface for action button items in the action list variant.
 */
export interface ActionItem {
  id: string;
  title: string;
  onPress: () => void;
}

/**
 * Interface for BottomSheetModal Props.
 */
interface BottomSheetModalProps {
  isVisible: boolean;
  onClose: () => void;
  actions: ActionItem[];
  cancelTitle?: string;
}

/**
 * The BottomSheetModal component renders a modal at the bottom of the screen with action buttons.
 *
 * Displays a grouped list of action buttons with dividers, and a separated cancel button at the bottom.
 *
 * @component
 * @param {boolean} isVisible - Whether the modal is visible or not. Default is false.
 * @param {function} props.onClose - A callback function that is triggered when the modal is closed or cancel is pressed. Default is () => {}.
 * @param {ActionItem[]} props.actions - Array of action items (excludes cancel button).
 * @param {string} props.cancelTitle - Title for the cancel button. Default is 'Cancel'.
 */
const BottomSheetModal = ({
  isVisible = false,
  onClose = () => {},
  actions,
  cancelTitle = 'Cancel',
}: BottomSheetModalProps) => {
  const renderActionList = () => {
    return (
      <View style={styles.container}>
        {/* Grouped action buttons */}
        {actions.length > 0 && (
          <View style={styles.actionGroup}>
            {actions.map((action, index) => (
              <React.Fragment key={action.id}>
                <TouchableOpacity
                  style={[
                    styles.baseButton,
                    styles.groupedButton,
                    index === 0 && styles.firstButton,
                    index === actions.length - 1 && styles.lastButton,
                  ]}
                  onPress={action.onPress}>
                  <Text style={styles.baseButtonText}>{action.title}</Text>
                </TouchableOpacity>
                {index < actions.length - 1 && <View style={styles.divider} />}
              </React.Fragment>
            ))}
          </View>
        )}

        {/* Always present cancel button */}
        <View style={styles.cancelGroup}>
          <TouchableOpacity
            style={[styles.baseButton, styles.cancelButton]}
            onPress={onClose}>
            <Text style={[styles.baseButtonText, styles.cancelButtonText]}>
              {cancelTitle}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={onClose}
      onSwipeComplete={onClose}
      swipeDirection="down"
      style={styles.modal}
      backdropOpacity={0.5}
      backdropTransitionOutTiming={0}
      statusBarTranslucent>
      {renderActionList()}
    </Modal>
  );
};

// Constants for consistent sizing
const BUTTON_WIDTH = deviceWidth - 40;
const BUTTON_HEIGHT = 56;
const BORDER_RADIUS = 12;

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  container: {
    ...center,
    marginBottom: 20,
  } as ViewStyle,
  actionGroup: {
    width: BUTTON_WIDTH,
    backgroundColor: colors.grayishBlue,
    borderRadius: BORDER_RADIUS,
    overflow: 'hidden',
  } as ViewStyle,
  cancelGroup: {
    width: '100%',
    alignItems: 'center',
    marginTop: 8,
  } as ViewStyle,
  baseButton: {
    ...center,
    width: '100%',
    height: BUTTON_HEIGHT,
  } as ViewStyle,
  groupedButton: {
    backgroundColor: 'transparent',
  } as ViewStyle,
  cancelButton: {
    width: BUTTON_WIDTH,
    backgroundColor: colors.grayishBlue,
    borderRadius: BORDER_RADIUS,
    ...marginBottom10,
  } as ViewStyle,
  firstButton: {
    borderTopLeftRadius: BORDER_RADIUS,
    borderTopRightRadius: BORDER_RADIUS,
  } as ViewStyle,
  lastButton: {
    borderBottomLeftRadius: BORDER_RADIUS,
    borderBottomRightRadius: BORDER_RADIUS,
  } as ViewStyle,
  divider: {
    height: StyleSheet.hairlineWidth,
    backgroundColor: colors.buttonGray,
  } as ViewStyle,
  baseButtonText: {
    ...buttonText,
    ...centerText,
    color: colors.blue600,
  } as TextStyle,
  cancelButtonText: {
    color: colors.blue700,
    fontWeight: 'bold',
  } as TextStyle,
});

export default BottomSheetModal;
