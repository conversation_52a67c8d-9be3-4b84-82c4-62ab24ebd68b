import React from 'react';
import { View, StyleSheet, Text, TouchableOpacity, TextStyle, ViewStyle } from 'react-native';
import Modal from 'react-native-modal';
import colors from '~/styles/colors';
import { marginBottom10 } from '~/styles/spacing';
import { buttonText, centerText } from '~/styles/text';
import { center, deviceWidth } from '~/styles/views';

/**
 * Interface for action button items in the action list variant.
 */
export interface ActionItem {
  id: string;
  title: string;
  onPress: () => void;
  style?: 'default' | 'cancel';
}

/**
 * Interface for BottomSheetModal Props.
 */
interface BottomSheetModalProps {
  isVisible: boolean;
  onClose: () => void;
  children?: React.ReactNode;
  variant?: 'default' | 'actionList';
  actions?: ActionItem[];
}

/**
 * The BottomSheetModal component renders a modal at the bottom of the screen with customizable options.
 *
 * Supports two variants:
 * - 'default': Basic modal that renders children (backward compatible)
 * - 'actionList': Pre-styled modal with action buttons for common use cases
 *
 * @component
 * @param {boolean} isVisible - Whether the modal is visible or not. Default is false.
 * @param {function} props.onClose - A callback function that is triggered when the modal is closed. Default is () => {}.
 * @param {React.ReactNode} props.children - Content displayed in default variant. Optional.
 * @param {'default' | 'actionList'} props.variant - The variant of the modal. Default is 'default'.
 * @param {ActionItem[]} props.actions - Array of action items for actionList variant. Optional.
 */
const BottomSheetModal = ({
  isVisible = false,
  onClose = () => {},
  children,
  variant = 'default',
  actions = [],
}: BottomSheetModalProps) => {
  const renderActionList = () => {
    // Separate cancel actions from regular actions
    const regularActions = actions.filter(action => action.style !== 'cancel');
    const cancelActions = actions.filter(action => action.style === 'cancel');

    return (
      <View style={styles.actionListContainer}>
        {/* Regular action buttons - grouped with dividers */}
        {regularActions.length > 0 && (
          <View style={styles.actionGroup}>
            {regularActions.map((action, index) => (
              <View key={action.id}>
                <TouchableOpacity
                  style={[
                    styles.actionButton,
                    index === 0 && styles.firstActionButton,
                    index === regularActions.length - 1 && styles.lastActionButton,
                  ]}
                  onPress={action.onPress}>
                  <Text style={styles.actionButtonText}>{action.title}</Text>
                </TouchableOpacity>
                {/* Divider between buttons (except for last button) */}
                {index < regularActions.length - 1 && (
                  <View style={styles.actionDivider} />
                )}
              </View>
            ))}
          </View>
        )}

        {/* Cancel buttons (separated and distinct) */}
        {cancelActions.length > 0 && (
          <View style={styles.cancelGroup}>
            {cancelActions.map((action) => (
              <TouchableOpacity
                key={action.id}
                style={[styles.actionButton, styles.cancelButton]}
                onPress={action.onPress}>
                <Text style={[styles.actionButtonText, styles.cancelButtonText]}>
                  {action.title}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </View>
    );
  };

  const renderContent = () => {
    if (variant === 'actionList') {
      return renderActionList();
    }
    return children && <View>{children}</View>;
  };

  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={onClose}
      onSwipeComplete={onClose}
      swipeDirection="down"
      style={styles.modal}
      backdropOpacity={0.5}
      backdropTransitionOutTiming={0}
      statusBarTranslucent>
      {renderContent()}
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  actionListContainer: {
    ...center,
    marginBottom: 20,
  } as ViewStyle,
  actionGroup: {
    width: deviceWidth - 40,
    backgroundColor: colors.grayishBlue,
    borderRadius: 12,
    overflow: 'hidden', // Ensures rounded corners work with dividers
  } as ViewStyle,
  cancelGroup: {
    width: '100%',
    alignItems: 'center',
    marginTop: 8, // Add separation between regular actions and cancel
  } as ViewStyle,
  actionButton: {
    ...center,
    width: deviceWidth - 40,
    height: 56, // Reduced from 65 to make buttons thinner
    // backgroundColor: 'transparent', // Background handled by group container
    // borderRadius: 0, // No individual border radius
    // marginBottom: 0, // No individual margins
  } as ViewStyle,
  firstActionButton: {
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  } as ViewStyle,
  lastActionButton: {
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
  } as ViewStyle,
  actionDivider: {
    height: StyleSheet.hairlineWidth,
    backgroundColor: colors.buttonGray,
    width: '100%',
  } as ViewStyle,
  cancelButton: {
    width: deviceWidth - 40,
    height: 56, // Same thinner height
    backgroundColor: colors.grayishBlue,
    borderRadius: 12,
    ...marginBottom10,
  } as ViewStyle,
  actionButtonText: {
    ...buttonText,
    ...centerText,
    color: colors.blue600,
    // fontSize: 17,
    // fontWeight: 400,
  } as TextStyle,
  cancelButtonText: {
    color: colors.blue700,
    fontWeight: 'bold',
  } as TextStyle,
});

export default BottomSheetModal;
