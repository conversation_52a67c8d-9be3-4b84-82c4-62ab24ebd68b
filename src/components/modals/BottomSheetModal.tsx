import React from 'react';
import { View, StyleSheet, Text, TouchableOpacity, TextStyle, ViewStyle } from 'react-native';
import Modal from 'react-native-modal';
import colors from '~/styles/colors';
import { marginBottom10 } from '~/styles/spacing';
import { buttonText, centerText } from '~/styles/text';
import { center, deviceWidth } from '~/styles/views';

/**
 * Interface for action button items in the action list variant.
 */
export interface ActionItem {
  id: string;
  title: string;
  onPress: () => void;
  style?: 'default' | 'cancel';
}

/**
 * Interface for BottomSheetModal Props.
 */
interface BottomSheetModalProps {
  isVisible: boolean;
  onClose: () => void;
  children?: React.ReactNode;
  variant?: 'default' | 'actionList';
  actions?: ActionItem[];
}

/**
 * The BottomSheetModal component renders a modal at the bottom of the screen with customizable options.
 *
 * Supports two variants:
 * - 'default': Basic modal that renders children (backward compatible)
 * - 'actionList': Pre-styled modal with action buttons for common use cases
 *
 * @component
 * @param {boolean} isVisible - Whether the modal is visible or not. Default is false.
 * @param {function} props.onClose - A callback function that is triggered when the modal is closed. Default is () => {}.
 * @param {React.ReactNode} props.children - Content displayed in default variant. Optional.
 * @param {'default' | 'actionList'} props.variant - The variant of the modal. Default is 'default'.
 * @param {ActionItem[]} props.actions - Array of action items for actionList variant. Optional.
 */
const BottomSheetModal = ({
  isVisible = false,
  onClose = () => {},
  children,
  variant = 'default',
  actions = [],
}: BottomSheetModalProps) => {
  const renderActionList = () => {
    return (
      <View style={styles.actionListContainer}>
        {actions.map((action) => (
          <TouchableOpacity
            key={action.id}
            style={styles.actionButton}
            onPress={action.onPress}>
            <Text style={styles.actionButtonText}>{action.title}</Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderContent = () => {
    if (variant === 'actionList') {
      return renderActionList();
    }
    return children && <View>{children}</View>;
  };

  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={onClose}
      onSwipeComplete={onClose}
      swipeDirection="down"
      style={styles.modal}
      backdropOpacity={0.5}
      backdropTransitionOutTiming={0}
      statusBarTranslucent>
      {renderContent()}
    </Modal>
  );
};

const styles = StyleSheet.create({
  modal: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  actionListContainer: {
    ...center,
    marginBottom: 20,
  } as ViewStyle,
  actionButton: {
    ...center,
    width: deviceWidth - 40,
    height: 65,
    borderRadius: 20,
    backgroundColor: colors.grayishBlue,
    ...marginBottom10,
  } as ViewStyle,
  actionButtonText: {
    ...buttonText,
    ...centerText,
    color: colors.blue600,
  } as TextStyle,
});

export default BottomSheetModal;
