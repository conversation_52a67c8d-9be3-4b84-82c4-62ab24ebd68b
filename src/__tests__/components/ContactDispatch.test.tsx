import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import ContactDispatch from '~/components/ContactDispatch';

// Mock the services
jest.mock('~/services/dispatch/CallService', () => ({
  callPhoneNumber: jest.fn(),
}));

jest.mock('~/services/dispatch/SmsService', () => ({
  sendSMS: jest.fn(),
}));

// Mock BottomSheetModal component
jest.mock('~/components/modals/BottomSheetModal', () => {
  return ({
    isVisible,
    actions,
    cancelTitle = 'Cancel',
    onClose,
  }: any) => {
    const _React = require('react');
    if (!isVisible) return null;

    return _React.createElement('View', { testID: 'bottom-sheet-modal' }, [
      // Regular actions
      ...(actions || []).map((action: any) =>
        _React.createElement(
          'Text',
          { key: action.id, onPress: action.onPress },
          action.title,
        ),
      ),
      // Always present cancel button
      _React.createElement(
        'Text',
        { key: 'cancel', onPress: onClose, testID: 'cancel-action' },
        cancelTitle,
      ),
    ]);
  };
});

describe('ContactDispatch', () => {
  const mockOnClose = jest.fn();
  const mockCallPhoneNumber =
    require('~/services/dispatch/CallService').callPhoneNumber;
  const mockSendSMS = require('~/services/dispatch/SmsService').sendSMS;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing when visible', () => {
    const { getByTestId } = render(
      <ContactDispatch isVisible={true} onClose={mockOnClose} />,
    );

    expect(getByTestId('bottom-sheet-modal')).toBeTruthy();
  });

  it('does not render when not visible', () => {
    const { queryByTestId } = render(
      <ContactDispatch isVisible={false} onClose={mockOnClose} />,
    );

    expect(queryByTestId('bottom-sheet-modal')).toBeNull();
  });

  it('renders all action buttons when visible', () => {
    const { getByText } = render(
      <ContactDispatch isVisible={true} onClose={mockOnClose} />,
    );

    expect(getByText('Send Message ****** 675 0477')).toBeTruthy();
    expect(getByText('Call ****** 341 9640')).toBeTruthy();
    expect(getByText('Cancel')).toBeTruthy();
  });

  it('calls sendSMS when message button is pressed', () => {
    const { getByText } = render(
      <ContactDispatch isVisible={true} onClose={mockOnClose} />,
    );

    const messageButton = getByText('Send Message ****** 675 0477');
    fireEvent.press(messageButton);

    expect(mockSendSMS).toHaveBeenCalledWith('****** 675 0477');
  });

  it('calls callPhoneNumber when call button is pressed', () => {
    const { getByText } = render(
      <ContactDispatch isVisible={true} onClose={mockOnClose} />,
    );

    const callButton = getByText('Call ****** 341 9640');
    fireEvent.press(callButton);

    expect(mockCallPhoneNumber).toHaveBeenCalledWith('****** 341 9640');
  });

  it('calls onClose when cancel button is pressed', () => {
    const { getByText } = render(
      <ContactDispatch isVisible={true} onClose={mockOnClose} />,
    );

    const cancelButton = getByText('Cancel');
    fireEvent.press(cancelButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('handles multiple button presses', () => {
    const { getByText } = render(
      <ContactDispatch isVisible={true} onClose={mockOnClose} />,
    );

    const messageButton = getByText('Send Message ****** 675 0477');
    const callButton = getByText('Call ****** 341 9640');
    const cancelButton = getByText('Cancel');

    fireEvent.press(messageButton);
    fireEvent.press(callButton);
    fireEvent.press(cancelButton);

    expect(mockSendSMS).toHaveBeenCalledTimes(1);
    expect(mockCallPhoneNumber).toHaveBeenCalledTimes(1);
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('handles rapid button presses', () => {
    const { getByText } = render(
      <ContactDispatch isVisible={true} onClose={mockOnClose} />,
    );

    const messageButton = getByText('Send Message ****** 675 0477');

    fireEvent.press(messageButton);
    fireEvent.press(messageButton);
    fireEvent.press(messageButton);

    expect(mockSendSMS).toHaveBeenCalledTimes(3);
  });

  it('renders with correct button text', () => {
    const { getByText } = render(
      <ContactDispatch isVisible={true} onClose={mockOnClose} />,
    );

    expect(getByText('Send Message ****** 675 0477')).toBeTruthy();
    expect(getByText('Call ****** 341 9640')).toBeTruthy();
    expect(getByText('Cancel')).toBeTruthy();
  });

  it('toggles visibility correctly', () => {
    const { rerender, getByTestId, queryByTestId } = render(
      <ContactDispatch isVisible={false} onClose={mockOnClose} />,
    );

    expect(queryByTestId('bottom-sheet-modal')).toBeNull();

    rerender(<ContactDispatch isVisible={true} onClose={mockOnClose} />);

    expect(getByTestId('bottom-sheet-modal')).toBeTruthy();
  });

  it('maintains button functionality after visibility toggle', () => {
    const { rerender, getByText } = render(
      <ContactDispatch isVisible={false} onClose={mockOnClose} />,
    );

    rerender(<ContactDispatch isVisible={true} onClose={mockOnClose} />);

    const messageButton = getByText('Send Message ****** 675 0477');
    fireEvent.press(messageButton);

    expect(mockSendSMS).toHaveBeenCalledWith('****** 675 0477');
  });
});
