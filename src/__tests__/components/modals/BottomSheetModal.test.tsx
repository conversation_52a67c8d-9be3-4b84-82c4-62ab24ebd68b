import React from 'react';
import { Text } from 'react-native';
import { render, fireEvent } from '@testing-library/react-native';
import BottomSheetModal, { ActionItem } from '~/components/modals/BottomSheetModal';

jest.mock('react-native-modal', () => {
  const { View } = require('react-native');
  return ({ children, isVisible }: any) => {
    return isVisible ? <View testID="modal">{children}</View> : null;
  };
});

describe('BottomSheetModal', () => {
  const mockOnClose = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('default variant', () => {
    it('renders children when visible', () => {
      const { getByText, getByTestId } = render(
        <BottomSheetModal isVisible={true} onClose={mockOnClose}>
          <Text>Test Content</Text>
        </BottomSheetModal>
      );

      expect(getByTestId('modal')).toBeTruthy();
      expect(getByText('Test Content')).toBeTruthy();
    });

    it('does not render when not visible', () => {
      const { queryByTestId } = render(
        <BottomSheetModal isVisible={false} onClose={mockOnClose}>
          <Text>Test Content</Text>
        </BottomSheetModal>
      );

      expect(queryByTestId('modal')).toBeNull();
    });
  });

  describe('actionList variant', () => {
    const mockActions: ActionItem[] = [
      {
        id: 'action1',
        title: 'Action 1',
        onPress: jest.fn(),
      },
      {
        id: 'action2',
        title: 'Action 2',
        onPress: jest.fn(),
      },
      {
        id: 'cancel',
        title: 'Cancel',
        onPress: jest.fn(),
        style: 'cancel',
      },
    ];

    it('renders action buttons when visible', () => {
      const { getByText, getByTestId } = render(
        <BottomSheetModal
          isVisible={true}
          onClose={mockOnClose}
          variant="actionList"
          actions={mockActions}
        />
      );

      expect(getByTestId('modal')).toBeTruthy();
      expect(getByText('Action 1')).toBeTruthy();
      expect(getByText('Action 2')).toBeTruthy();
      expect(getByText('Cancel')).toBeTruthy();
    });

    it('calls action onPress when button is pressed', () => {
      const { getByText } = render(
        <BottomSheetModal
          isVisible={true}
          onClose={mockOnClose}
          variant="actionList"
          actions={mockActions}
        />
      );

      fireEvent.press(getByText('Action 1'));
      expect(mockActions[0].onPress).toHaveBeenCalled();

      fireEvent.press(getByText('Action 2'));
      expect(mockActions[1].onPress).toHaveBeenCalled();
    });

    it('does not render when not visible', () => {
      const { queryByTestId } = render(
        <BottomSheetModal
          isVisible={false}
          onClose={mockOnClose}
          variant="actionList"
          actions={mockActions}
        />
      );

      expect(queryByTestId('modal')).toBeNull();
    });

    it('handles empty actions array', () => {
      const { getByTestId, queryByText } = render(
        <BottomSheetModal
          isVisible={true}
          onClose={mockOnClose}
          variant="actionList"
          actions={[]}
        />
      );

      expect(getByTestId('modal')).toBeTruthy();
      expect(queryByText('Action 1')).toBeNull();
    });

    it('separates cancel actions from regular actions', () => {
      const mixedActions: ActionItem[] = [
        {
          id: 'regular1',
          title: 'Regular Action 1',
          onPress: jest.fn(),
        },
        {
          id: 'regular2',
          title: 'Regular Action 2',
          onPress: jest.fn(),
        },
        {
          id: 'cancel1',
          title: 'Cancel',
          onPress: jest.fn(),
          style: 'cancel',
        },
      ];

      const { getByText } = render(
        <BottomSheetModal
          isVisible={true}
          onClose={mockOnClose}
          variant="actionList"
          actions={mixedActions}
        />
      );

      expect(getByText('Regular Action 1')).toBeTruthy();
      expect(getByText('Regular Action 2')).toBeTruthy();
      expect(getByText('Cancel')).toBeTruthy();
    });
  });

  describe('backward compatibility', () => {
    it('defaults to default variant when no variant is specified', () => {
      const { getByText, getByTestId } = render(
        <BottomSheetModal isVisible={true} onClose={mockOnClose}>
          <Text>Legacy Content</Text>
        </BottomSheetModal>
      );

      expect(getByTestId('modal')).toBeTruthy();
      expect(getByText('Legacy Content')).toBeTruthy();
    });
  });
});
