import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import BottomSheetModal, {
  ActionItem,
} from '~/components/modals/BottomSheetModal';

jest.mock('react-native-modal', () => {
  const { View } = require('react-native');
  return ({ children, isVisible }: any) => {
    return isVisible ? <View testID="modal">{children}</View> : null;
  };
});

describe('BottomSheetModal', () => {
  const mockOnClose = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });



  describe('BottomSheetModal', () => {
    const mockActions: ActionItem[] = [
      {
        id: 'action1',
        title: 'Action 1',
        onPress: jest.fn(),
      },
      {
        id: 'action2',
        title: 'Action 2',
        onPress: jest.fn(),
      },
    ];

    it('renders action buttons and cancel button when visible', () => {
      const { getByText, getByTestId } = render(
        <BottomSheetModal
          isVisible={true}
          onClose={mockOnClose}
          actions={mockActions}
        />,
      );

      expect(getByTestId('modal')).toBeTruthy();
      expect(getByText('Action 1')).toBeTruthy();
      expect(getByText('Action 2')).toBeTruthy();
      expect(getByText('Cancel')).toBeTruthy(); // Always present
    });

    it('calls action onPress when button is pressed', () => {
      const { getByText } = render(
        <BottomSheetModal
          isVisible={true}
          onClose={mockOnClose}
          actions={mockActions}
        />,
      );

      fireEvent.press(getByText('Action 1'));
      expect(mockActions[0].onPress).toHaveBeenCalled();

      fireEvent.press(getByText('Action 2'));
      expect(mockActions[1].onPress).toHaveBeenCalled();
    });

    it('calls onClose when cancel button is pressed', () => {
      const { getByText } = render(
        <BottomSheetModal
          isVisible={true}
          onClose={mockOnClose}
          actions={mockActions}
        />,
      );

      fireEvent.press(getByText('Cancel'));
      expect(mockOnClose).toHaveBeenCalled();
    });

    it('does not render when not visible', () => {
      const { queryByTestId } = render(
        <BottomSheetModal
          isVisible={false}
          onClose={mockOnClose}
          actions={mockActions}
        />,
      );

      expect(queryByTestId('modal')).toBeNull();
    });

    it('handles empty actions array but still shows cancel', () => {
      const { getByTestId, queryByText, getByText } = render(
        <BottomSheetModal
          isVisible={true}
          onClose={mockOnClose}
          actions={[]}
        />,
      );

      expect(getByTestId('modal')).toBeTruthy();
      expect(queryByText('Action 1')).toBeNull();
      expect(getByText('Cancel')).toBeTruthy(); // Cancel is always present
    });

    it('supports custom cancel title', () => {
      const { getByText } = render(
        <BottomSheetModal
          isVisible={true}
          onClose={mockOnClose}
          actions={mockActions}
          cancelTitle="Close"
        />,
      );

      expect(getByText('Close')).toBeTruthy();
      expect(() => getByText('Cancel')).toThrow();
    });
  });
});
