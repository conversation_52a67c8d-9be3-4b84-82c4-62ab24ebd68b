import axios from 'axios';
import Config from 'react-native-config';
import * as Keychain from 'react-native-keychain';
import TokenService from '~/services/TokenService';
import { SentryService } from '~/services/SentryService';

export const axiosPrivate = axios.create({
  baseURL: Config.API_URL,
  headers: { 'Content-Type': 'application/json' },
  withCredentials: true,
});

// Alter defaults after instance has been created
// instance.defaults.headers.common.Authorization = AUTH_TOKEN;

axiosPrivate.interceptors.request.use(
  async config => {
    config.headers = config.headers || {};
    const accessToken = await TokenService.getAccessToken();

    if (accessToken && !config.headers.Authorization) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }

    return config;
  },
  error => {
    // Do something with request error
    return Promise.reject(error);
  },
);

axiosPrivate.interceptors.response.use(
  response => {
    // Any status code that lie within the range of 2xx cause this function to trigger
    // Do something with response data

    return response;
  },
  async error => {
    // Any status codes that falls outside the range of 2xx cause this function to trigger
    // Do something with response error
    const prevRequest = error?.config;
    const httpStatusCode = error?.response?.status;

    console.error(
      `AxiosService.ts: response.use: error: ${error}, request`,
      prevRequest,
    );

    if (
      (httpStatusCode === 401 || httpStatusCode === 403) &&
      !prevRequest?.sent
    ) {
      prevRequest.sent = true;

      try {
        const newTokens = await TokenService.refreshAccessToken();

        if (newTokens) {
          prevRequest.headers.Authorization = `Bearer ${newTokens.accessToken}`;
          return axiosPrivate(prevRequest);
        }
      } catch (refreshError) {
        await SentryService.logSentryError({
          error: refreshError as Error,
          tags: {
            file: 'AxiosService.ts',
            function: 'response_interceptor',
            eventType: 'token_refresh_failed',
          },
          level: 'error',
        });
      }
    }

    return Promise.reject(error);
  },
);

export const getRefreshTokenFromKeychain = async () => {
  try {
    return await Keychain.getGenericPassword();
  } catch (error) {
    console.error(
      'AxiosService.ts: getRefreshTokenFromKeychain(): Failed to access Keychain',
      error,
    );

    return null;
  }
};
