import { createNativeStackNavigator } from '@react-navigation/native-stack';
import React from 'react';
import SubHeader from '~/components/headers/SubHeader';
import { AddParcelProvider } from '~/screens/parcel/AddParcelProvider';
import ParcelInformationScreen from '~/screens/parcel/ParcelInformationScreen';
import ParcelProofOfServiceScreen from '~/screens/parcel/ParcelProofOfServiceScreen';
import WorkOrderParcelScreen from '~/screens/parcel/WorkOrderParcelScreen';
import EditParcelScreen from '~/screens/parcel/EditParcelScreen';
import SelectParcelTypeScreen from '~/screens/parcel/SelectParcelTypeScreen';
import { Stop, StopType, TaskListItem } from '~/types/stops.types';
import { RouteProp } from '@react-navigation/native';
import colors from '~/styles/colors';
import { validateThatDataIsNotEmpty } from '~/utils/validation/data';
import en from '~/localization/en';
import { Parcel } from '~/types/parcel.types';

export type RootStackParamList = {
  SelectParcelTypeScreen: {
    stop: Stop;
    stopList: Stop[];
    task: TaskListItem;
    numberOfServices: number;
    type: StopType;
  };
  ParcelInformationScreen: { type: StopType };
  ParcelProofOfServiceScreen: { type: StopType };
  WorkOrderParcelScreen: {
    type: StopType;
    parcel: Parcel;
    stop: Stop;
  };
  EditParcelScreen: {
    stop: Stop;
    stopId: string;
    stopList: Stop[];
    parcel: Parcel;
    type: StopType;
  };
};

const Stack = createNativeStackNavigator<RootStackParamList>();

function ParcelStack({ route }: { route: any }) {
  const { stop, stopList, parcelTypes, stopType, initialParcelData } =
    route.params ?? {};

  return (
    <AddParcelProvider
      stop={stop}
      stopList={stopList}
      parcelTypes={parcelTypes}
      stopType={stopType}
      initialParcelData={initialParcelData}>
      <Stack.Navigator
        initialRouteName="SelectParcelTypeScreen"
        screenOptions={{ headerShadowVisible: false }}>
        <Stack.Screen
          name="SelectParcelTypeScreen"
          component={SelectParcelTypeScreen}
          options={getScreenOptions('1')}
        />

        <Stack.Screen
          name="ParcelInformationScreen"
          component={ParcelInformationScreen}
          options={getScreenOptions('2')}
        />

        <Stack.Screen
          name="ParcelProofOfServiceScreen"
          component={ParcelProofOfServiceScreen}
          options={getScreenOptions('3')}
        />

        <Stack.Screen
          name="WorkOrderParcelScreen"
          component={WorkOrderParcelScreen}
          options={() => ({
            header: () => <SubHeader title={en.confirm_parcel} />,
          })}
        />

        <Stack.Screen
          name="EditParcelScreen"
          component={EditParcelScreen}
          options={() => ({
            header: () => <SubHeader title={en.edit_parcel} />,
          })}
        />
      </Stack.Navigator>
    </AddParcelProvider>
  );
}

function getScreenOptions(screenNumber: string) {
  return ({
    route: routeData,
    options,
  }: {
    route: RouteProp<RootStackParamList, keyof RootStackParamList>;
    options: any;
  }) => {
    const isWorkOrderScreen = routeData.name === 'WorkOrderParcelScreen';

    const title = isWorkOrderScreen
      ? en.add_a_parcel
      : validateThatDataIsNotEmpty(routeData.params?.stop?.Type__c ?? '')
        ? (routeData.params?.stop?.Type__c ?? en.add_a_parcel)
        : en.add_a_parcel;

    return {
      header: () => (
        <SubHeader
          route={routeData}
          options={options}
          title={title}
          showContactDispatch={false}
          showProgressText={true}
          progressHeaderText={`${screenNumber}/3`}
          backgroundColor={colors.backgroundLight}
        />
      ),
      headerBackVisible: false,
    };
  };
}

export default ParcelStack;
