import Realm from 'realm';

function migrateRouteOffsets(oldRealm: Realm, newRealm: Realm) {
  const oldRoutes = oldRealm.objects('Route_Summary__c');
  const newRoutes = newRealm.objects('Route_Summary__c');

  for (let i = 0; i < oldRoutes.length; i++) {
    const oldOffset = oldRoutes[i].UTC_Offset__c;
    let newOffset: number | null = null;

    if (typeof oldOffset === 'string' && oldOffset.trim() !== '') {
      const parsed = parseFloat(oldOffset);
      newOffset = isNaN(parsed) ? null : parsed;
    }

    newRoutes[i].UTC_Offset__c = newOffset;
  }
}

function migrateTemperatureFields(oldRealm: Realm, newRealm: Realm) {
  const oldDailySchedules = oldRealm.objects('Daily_Schedule__c');
  const newDailySchedules = newRealm.objects('Daily_Schedule__c');

  for (let i = 0; i < oldDailySchedules.length; i++) {
    newDailySchedules[i].High_Temperature__c = null;
  }

  const oldRoutes = oldRealm.objects('Route_Summary__c');
  const newRoutes = newRealm.objects('Route_Summary__c');

  for (let i = 0; i < oldRoutes.length; i++) {
    newRoutes[i].High_Temperature__c = null;
  }
}

function migrateWorkOrderImages(oldRealm: Realm, newRealm: Realm) {
  const oldImages = oldRealm.objects('Image');
  const newImages = newRealm.objects('Image');

  for (let i = 0; i < oldImages.length; i++) {
    const oldImage = oldImages[i] as any;
    const newImage = newImages[i] as any;

    if (oldImage.RouteSummaryId === '') {
      newImage.RouteSummaryId = null;
    }
  }
}

export function migrateRealm(oldRealm: Realm, newRealm: Realm) {
  if (oldRealm.schemaVersion < 2) {
    migrateRouteOffsets(oldRealm, newRealm);
  }

  if (oldRealm.schemaVersion < 4) {
    migrateTemperatureFields(oldRealm, newRealm);
  }

  if (oldRealm.schemaVersion < 5) {
    migrateWorkOrderImages(oldRealm, newRealm);
  }
}
