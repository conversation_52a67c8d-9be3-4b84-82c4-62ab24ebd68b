import React, { useEffect, useState } from 'react';
import { ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '~/navigation/ParcelStack';
import ScreenWrapper from '~/screens/ScreenWrapper';
import { useAddParcelContext } from '~/screens/parcel/AddParcelProvider';
import FilledButton from '~/components/buttons/FilledButton';
import CardWrapper from '~/components/cards/CardWrapper';
import en from '~/localization/en';
import { buttonFullWidth, primarySolidButton } from '~/styles/buttons';
import TextInput from '~/components/inputs/TextInput';
import PhotoCaptureWrapper from '~/components/camera/PhotoCaptureWrapper';
import StepperInput from '~/components/inputs/StepperInput';
import { ParcelManager } from '~/services/sync/parcelManager';
import { Pa<PERSON><PERSON> } from '~/types/parcel.types';
import { Stop, StopType } from '~/types/stops.types';
import { ImageTitle } from '~/utils/images';
import { ImageStore } from '~/services/ImageStoreService';
import { getStopNameById } from '~/db/realm/operations/stop.operations';

export interface WorkOrderParcelScreenProps {
  route: {
    params: {
      stop: Stop;
      parcel: Parcel;
      type: StopType;
    };
  };
  navigation: NativeStackNavigationProp<RootStackParamList>;
}

const WorkOrderParcelScreen: React.FC<WorkOrderParcelScreenProps> = ({
  route,
}) => {
  const { parcel, stop, type } = route.params;
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const { parcelData, setParcelQuantity, setPosPhoto } = useAddParcelContext();

  const [destinationAddress, setDestinationAddress] = useState('');

  useEffect(() => {
    const getDestinationAddress = async () => {
      if (parcelData.dropoffDestination) {
        const destination = await getStopNameById(
          parcelData.dropoffDestination,
        );
        setDestinationAddress(destination);
      } else {
        setDestinationAddress('');
      }
    };

    getDestinationAddress();
  }, [parcelData?.dropoffDestination]);

  const confirmParcel = async () => {
    try {
      await ParcelManager.updateParcel(parcel.Id, {
        Quantity__c: type === StopType.Pickup ? parcelData.quantity : undefined,
        Dropoff_Quantity__c:
          type === StopType.Delivery ? parcelData.quantity : undefined,
        Pickup_Scan_Time__c:
          type === StopType.Pickup ? new Date().toISOString() : undefined,
        Delivery_Scan_Time__c:
          type === StopType.Delivery ? new Date().toISOString() : undefined,
      });

      const base64String = parcelData.posPhoto?.includes('base64,')
        ? parcelData.posPhoto.split('base64,')[1]
        : parcelData.posPhoto;

      if (base64String) {
        await ImageStore.addImage({
          titleType: ImageTitle.parcel,
          stopId: stop.Id,
          parcelId: parcel.Id,
          routeSummaryId: stop.Summary__c,
          base64String,
        });
      }

      navigation.goBack();
    } catch (error) {
      console.error('WorkOrderParcelScreen: confirmParcel(): ', error);
    }
  };

  const parcelPhotoUri = !parcelData.posPhoto
    ? ''
    : `data:image/jpeg;base64,${parcelData.posPhoto}`;

  return (
    <ScreenWrapper>
      <ScreenWrapper.Body>
        <ScrollView showsVerticalScrollIndicator={false}>
          <CardWrapper>
            <TextInput
              label={en.type}
              defaultValue={parcelData.type?.Name}
              disabled
            />
          </CardWrapper>

          {type === StopType.Pickup && (
            <CardWrapper>
              <TextInput
                label={en.delivery_destination}
                defaultValue={
                  destinationAddress ?? parcelData.dropoffDestination
                }
                disabled
              />
            </CardWrapper>
          )}

          <CardWrapper>
            <TextInput
              label={en.barcode}
              defaultValue={parcelData.barcodeScannerResult}
              disabled
            />
          </CardWrapper>

          {type === StopType.Pickup ? (
            <CardWrapper title={en.quantity}>
              <StepperInput
                value={parcelData?.quantity ?? 1}
                setValue={setParcelQuantity}
                infoLabel={en.number_of_parcels}
              />
            </CardWrapper>
          ) : (
            <CardWrapper>
              <TextInput
                label={en.quantity}
                defaultValue={String(parcelData?.quantity ?? '')}
                disabled
              />
            </CardWrapper>
          )}

          {type === StopType.Pickup && (
            <CardWrapper>
              <PhotoCaptureWrapper
                imageTitle={en.take_parcel_photo}
                cameraTitle={'Take a proof of service photo'}
                imageData={parcelPhotoUri}
                onCapture={setPosPhoto}
              />
            </CardWrapper>
          )}
        </ScrollView>
      </ScreenWrapper.Body>

      <ScreenWrapper.Bottom>
        <FilledButton
          id={'WorkOrderParcelScreen.FilledButton.continue'}
          title={en.confirm}
          style={[buttonFullWidth, primarySolidButton]}
          isDisabled={type === StopType.Pickup && !parcelData.posPhoto}
          color="primary"
          onClick={confirmParcel}
          containerStyle={null}
        />
      </ScreenWrapper.Bottom>
    </ScreenWrapper>
  );
};

export default WorkOrderParcelScreen;
