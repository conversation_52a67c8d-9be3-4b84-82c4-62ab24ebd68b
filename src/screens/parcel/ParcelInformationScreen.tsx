import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TextStyle } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import FilledButton from '~/components/buttons/FilledButton';
import BottomSheetModalWithRadioButton from '~/components/modals/BottomSheetWithRadioButtonsList';
import { useAddParcelContext } from '~/screens/parcel/AddParcelProvider';
import colors from '~/styles/colors';
import en from '~/localization/en';
import { blackText, buttonText, fontSize16, h4, heading } from '~/styles/text';
import { primarySolidButton, buttonFullWidth } from '~/styles/buttons';
import { useObject } from '@realm/react';
import { RouteSummary } from '~/types/routes.types';
import { RouteSummarySchema } from '~/db/realm/schemas/route-summary.schema';
import ScreenWrapper from '~/screens/ScreenWrapper';
import CardWrapper from '~/components/cards/CardWrapper';
import DropoffDestinationSelector from '~/components/select/dropdown/DropoffDestinationSelector';
import RadioSelectionCard from '~/components/select/radio/RadioSelectionCard';
import { marginBottom10 } from '~/styles/spacing';
import TextInput from '~/components/inputs/TextInput';
import { useDestinationsData } from '~/utils/parcel';
import { StopType } from '~/types/stops.types';

const ParcelInformationScreen: React.FC = () => {
  const navigation = useNavigation();
  const {
    stopList,
    stop,
    stopType,
    parcelData,
    setDropoffDestination,
    setIsSignatureRequired,
    setComments,
    isContinueButtonEnabled,
  } = useAddParcelContext();

  const routeData = stop?.Summary__c
    ? useObject<RouteSummary>({
        type: RouteSummarySchema.name,
        primaryKey: stop.Summary__c,
      })
    : null;

  const isValidRouteData =
    !!stop?.Summary__c && !!routeData && typeof routeData === 'object';

  const [isModalVisible, setIsModalVisible] = useState(false);

  const destinationsData = useDestinationsData(stopList, stop.Id);

  useEffect(() => {
    const sortedStopList = [...stopList].sort((a, b) => {
      return (
        new Date(a.Stop_Time_Preferred__c ?? '').getTime() -
        new Date(b.Stop_Time_Preferred__c ?? '').getTime()
      );
    });

    const lastStop = sortedStopList[sortedStopList.length - 1];
    const isAtLastStop = stop?.Id === lastStop?.Id;

    if (isAtLastStop) {
      setDropoffDestination(lastStop.Id);
    } else if (
      isValidRouteData &&
      routeData.Default_Delivery_Destination__c === true &&
      destinationsData?.length > 0
    ) {
      setDropoffDestination(lastStop.Id);
    } else if (stopType === StopType.Delivery) {
      setDropoffDestination(stop.Id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    destinationsData,
    routeData?.Default_Delivery_Destination__c,
    stop?.Id,
    stopList,
  ]);

  const continueButtonPressed = () => {
    navigation.navigate('ParcelProofOfServiceScreen');
  };

  return (
    <ScreenWrapper isKeyboardSensitive>
      <ScreenWrapper.Body withoutPadding>
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <View style={styles.header}>
            <Text style={[heading, blackText] as TextStyle}>
              {en.parcel_information}
            </Text>
          </View>

          {stopType === StopType.Pickup && destinationsData.length > 0 && (
            <DropoffDestinationSelector
              destinations={destinationsData}
              selectedDestination={parcelData?.dropoffDestination}
              onOpenModal={() => setIsModalVisible(true)}
            />
          )}

          <RadioSelectionCard<boolean>
            label={en.signature_required}
            value={parcelData?.isSignatureRequired}
            onChange={setIsSignatureRequired}
            options={[
              { label: en.yes, value: true },
              { label: en.no, value: false },
            ]}
          />

          <CardWrapper>
            <TextInput
              label={en.add_comment}
              value={parcelData?.comments}
              placeholder={en.additional_information}
              onChangeText={setComments}
            />
          </CardWrapper>
        </ScrollView>

        <BottomSheetModalWithRadioButton
          id={'ParcelInformationScreen.BottomSheetModalWithRadioButton'}
          selectedId={parcelData?.dropoffDestination || null}
          items={destinationsData}
          isVisible={isModalVisible}
          onClose={() => setIsModalVisible(false)}
          onItemSelected={(id: string) => {
            setIsModalVisible(false);
            setDropoffDestination(id);
          }}
          title={en.dropoff_destination}
        />
      </ScreenWrapper.Body>
      <ScreenWrapper.Bottom>
        <FilledButton
          id={'ParcelInformationScreen.FilledButton.continue'}
          title={en.continue}
          style={[buttonFullWidth, primarySolidButton]}
          isDisabled={!isContinueButtonEnabled('second')}
          color="primary"
          onClick={continueButtonPressed}
        />
      </ScreenWrapper.Bottom>
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    ...marginBottom10,
  },
  scanBarcodeText: {
    ...(buttonText as TextStyle),
    color: colors.red400,
  },
  dropdownContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.lightGray,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  placeholderText: {
    color: colors.transBlack60,
  },
  optionText: {
    ...h4,
    ...blackText,
    ...fontSize16,
    paddingLeft: 10,
  },
  buttonContainer: {
    backgroundColor: colors.white,
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: colors.transGray20,
  },
});

export default ParcelInformationScreen;
