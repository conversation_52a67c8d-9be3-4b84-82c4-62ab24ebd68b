import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextStyle,
  Alert,
  ViewStyle,
} from 'react-native';
import colors from '~/styles/colors';
import { blackText, buttonText, heading } from '~/styles/text';
import { cardView, deviceWidth, row } from '~/styles/views';
import { Mobile } from '~/components/icons';
import { checkCameraPermissionsAndOpenIt } from '~/components/camera/CheckPermissionAndOpenCamera';
import { useNavigation } from '@react-navigation/native';
import { useAddParcelContext } from '~/screens/parcel/AddParcelProvider';
import FilledButton from '~/components/buttons/FilledButton';
import { buttonFullWidth, primarySolidButton } from '~/styles/buttons';
import { Parcel } from '~/types/parcel.types';
import { ParcelManager } from '~/services/sync/parcelManager';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import ScreenWrapper from '~/screens/ScreenWrapper';
import en from '~/localization/en';
import { ImageStore } from '~/services/ImageStoreService';
import { ImageTitle } from '~/utils/images';
import { marginBottom10 } from '~/styles/spacing';
import ScanBarcodeCard from '~/components/barcode/ScanBarcodeCard';
import { IMAGE_ASPECT_RATIO } from '~/utils/constants';
import PhotoCaptureWrapper from '~/components/camera/PhotoCaptureWrapper';
import CardWrapper from '~/components/cards/CardWrapper';
import { StopType } from '~/types/stops.types';

const ParcelProofOfServiceScreen: React.FC = () => {
  const {
    stop,
    parcelData,
    stopType,
    isContinueButtonEnabled,
    setPosPhoto,
    setBarcodeResult,
  } = useAddParcelContext();
  const navigation = useNavigation<NativeStackNavigationProp<any>>();

  const continueButtonPressed = async () => {
    try {
      const parcelObject: Partial<Parcel> = {
        Pickup__c: stopType === StopType.Pickup ? stop.Id : '',
        Delivery__c:
          stopType === StopType.Pickup
            ? parcelData.dropoffDestination
            : stop.Id,
        Requires_Signature__c: parcelData.isSignatureRequired ?? null,
        Reference_Required__c: !!parcelData.barcodeScannerResult,
        Parcel_Type_Name__c: parcelData.type?.Name ?? '',
        Parcel_Type_Definition__c: parcelData.type?.Id ?? '',
        Quantity__c: parcelData.quantity ?? null,
        Reference__c: parcelData.barcodeScannerResult,
        Comments__c: parcelData.comments,
      };

      const { success, message, parcelId } =
        await ParcelManager.createParcel(parcelObject);

      if (success) {
        if (parcelData.posPhoto) {
          ImageStore.addImage({
            titleType: ImageTitle.parcel,
            stopId: stop.Id,
            parcelId: parcelId,
            routeSummaryId: stop.Summary__c,
            base64String: parcelData.posPhoto,
          });
        }

        navigation.popToTop();
        navigation.goBack();
      } else {
        Alert.alert(message);
      }
    } catch (error) {
      console.error('ParcelProofOfServiceScreen: postData():', error);
    }
  };

  const navigateToPosBarcodeScanner = async () => {
    await checkCameraPermissionsAndOpenIt(navigation, setBarcodeResult, {
      title: en.scan_barcode_title,
      infoMessage: 'Hold your phone over the barcode',
      isBarCodeScanner: true,
      MessageIconComponent: Mobile,
    });
  };

  const parcelPhotoUri = !parcelData.posPhoto
    ? ''
    : `data:image/jpeg;base64,${parcelData.posPhoto}`;

  const barcodeString = parcelData.barcodeScannerResult ?? '';

  return (
    <ScreenWrapper>
      <ScreenWrapper.Body>
        <View style={styles.header}>
          <Text style={[heading, blackText] as TextStyle}>
            {en.parcel_proof_of_service}
          </Text>
        </View>

        <CardWrapper>
          <PhotoCaptureWrapper
            imageTitle={en.take_parcel_photo}
            cameraTitle={'Take a proof of service photo'}
            imageData={parcelPhotoUri}
            onCapture={setPosPhoto}
          />
        </CardWrapper>

        <ScanBarcodeCard
          barcode={barcodeString}
          onScan={navigateToPosBarcodeScanner}
          setBarcode={setBarcodeResult}
        />
      </ScreenWrapper.Body>
      <ScreenWrapper.Bottom>
        <FilledButton
          id={'ParcelProofOfServiceScreen.FilledButton.save'}
          title={en.save_parcel}
          style={[buttonFullWidth, primarySolidButton]}
          isDisabled={!isContinueButtonEnabled('third')}
          color="primary"
          onClick={continueButtonPressed}
          containerStyle={null}
        />
      </ScreenWrapper.Bottom>
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  card: {
    ...cardView,
    gap: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    ...marginBottom10,
  },
  signature: {
    height: deviceWidth * IMAGE_ASPECT_RATIO,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    borderColor: colors.backgroundLight,
  },
  photoPlaceholder: {
    height: deviceWidth * IMAGE_ASPECT_RATIO,
    backgroundColor: colors.white,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.darkBlue500,
    borderStyle: 'dashed',
  },
  row: {
    ...(row as ViewStyle),
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  redTextButton: {
    ...(buttonText as TextStyle),
    color: colors.red400,
  } as TextStyle,
});

export default ParcelProofOfServiceScreen;
