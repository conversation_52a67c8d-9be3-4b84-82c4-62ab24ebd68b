import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  Image,
  ViewStyle,
  Platform,
} from 'react-native';
import {
  Camera,
  CameraRuntimeError,
  CodeScanner,
  CodeType,
  getCameraDevice,
  useCameraDevices,
  useCodeScanner,
} from 'react-native-vision-camera';
import FilledButton from '~/components/buttons/FilledButton';
import { primarySolidButton } from '~/styles/buttons';
import {
  center,
  container,
  deviceHeight,
  deviceWidth,
  headerHeight,
  row,
} from '~/styles/views';
import BarcodeScannerOverlay from '~/components/barcode/BarcodeScannerOverlay';
import colors from '~/styles/colors';
import IconButton from '~/components/buttons/IconButton';
import {
  compressImageAndConvertToBase64,
  getRandomImgFile,
} from '~/utils/images';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import { CameraError } from '~/components/error/CameraError';
import en from '~/localization/en';
import { interpolateString, normalizeBarcodeValue } from '~/utils/strings';
import useFlashSetting from '~/hooks/useFlashSetting';
import TransparentHeader from '~/components/headers/TransparentHeader';
import { AutoFlash, Flash, CaptureButton, Cross } from '~/components/icons';
import { useAppState } from '~/hooks/useAppState';
import { useIsFocused } from '@react-navigation/core';

/**
 * A Camera View Screen is a component that opens up a camera with a title, flash button and a capture button.
 * After the photo is clicked, the clicked photo is visible to the user with Retake and Submit Button.
 * 
 * @component
 * @param {string} props.navigation - This is the navigation object.
 * @param {string} props.title - This is the header title text of the camera screen.
 * @param {boolean} props.isBarCodeScanner - This boolean variable will tell us to use a Camera for capturing image or scanning a barcode.
 * @param {MessageIconComponent} props.MessageIconComponent - This is component which is an icon that will display in the information container.
 * @param {function} props.getImagePathOrBarcodeResult - This is the callback function to send the image path or barcode result to parent component.
 * @param {any} props.infoMessage - Any message that needs to be displayed above the capture button.

 * @example
 * // Example usage of CameraViewScreen
 * 
 *  const title = 'Camera View';
 *  const isBarCodeScanner = true;
 *  const MessageIconComponent = <CameraSNOBoxIcon />;
 *  const getImagePathOrBarcodeResult = (imagePath: string) => {
 *    console.log('Image path or barcode result: ', imagePath);
 *  };
 *  const infoMessage = 'Take the photo of the building';
 *  
 *  navigation.navigate('CameraViewScreen', {
 *    title,
 *    isBarCodeScanner,
 *    MessageIconComponent,
 *    getImagePathOrBarcodeResult,
 *    infoMessage,
 *  });
 */

type CameraViewScreenProps = {
  navigation: any;
  route: {
    params: {
      title: string;
      isBarCodeScanner: boolean;
      MessageIconComponent: React.ComponentType<any>;
      infoMessage?: string;
      getImagePathOrBarcodeResult: (imagePath: string) => void;
    };
  };
};

const supportedCodeTypes: CodeType[] = Platform.select({
  ios: [
    'qr',
    'ean-13',
    'ean-8',
    'upc-a',
    'upc-e',
    'code-128',
    'code-39',
    'code-93',
    'pdf-417',
    'aztec',
    'data-matrix',
  ],
  android: ['qr', 'ean-13', 'ean-8', 'upc-a', 'code-128'],
}) ?? ['qr', 'ean-13']; // Fallback default

const previewHeight =
  Platform.OS === 'android'
    ? (deviceWidth / 3) * 4
    : deviceHeight - headerHeight;

const CameraViewScreen: React.FC<CameraViewScreenProps> = ({
  navigation,
  route,
}) => {
  const {
    title,
    isBarCodeScanner,
    infoMessage,
    MessageIconComponent,
    getImagePathOrBarcodeResult,
  } = route.params;

  const insets = useSafeAreaInsets();
  const devices = useCameraDevices();
  const camera = useRef<Camera>(null);
  const device = getCameraDevice(devices, 'back');
  const [isCapturingPhoto, setIsCapturingPhoto] = useState(false);
  const [isMessageVisible, setIsMessageVisible] = useState(true);
  const [photoPath, setPhotoPath] = useState<string | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [isCameraReady, setIsCameraReady] = useState(false);
  const [isForeground, setIsForeground] = useState(true);
  const { flashSetting, toggleFlashSetting } = useFlashSetting();
  const isFocussed = useIsFocused();
  const isReadyToCapturePhoto = photoPath === null && !isCapturingPhoto;
  const isActive = isFocussed && isForeground;

  useAppState({
    onForeground: async () => {
      setIsForeground(true);
    },
    onBackground: () => {
      setIsForeground(false);
    },
  });

  const handleBackPress = useCallback(() => {
    if (photoPath === null) {
      navigation.goBack();
    } else {
      setPhotoPath(null);
    }
  }, [photoPath, navigation]);

  const handleCapturePhoto = async () => {
    if (isCapturingPhoto || !isCameraReady) return;

    try {
      setIsCapturingPhoto(true);
      if (!camera.current) throw new CameraError('Camera ref is null!');

      const shouldUseFlash = device?.hasFlash && flashSetting !== 'off';
      const flashOption = shouldUseFlash ? flashSetting : 'off';
      const photo = await camera.current.takePhoto({
        flash: flashOption,
        enableShutterSound: false,
      });

      if (!photo?.path) throw new CameraError('No photo captured');

      setPhotoPath(photo.path);
    } catch (error) {
      const errorMessage =
        error instanceof CameraError
          ? error.message
          : en.unexpected_error_occurred_while_capturing_the_photo;

      const errorMessageForDisplay = interpolateString(
        en.failed_to_take_photo,
        { error: errorMessage },
      );

      Alert.alert(en.error, errorMessageForDisplay);
    } finally {
      setIsCapturingPhoto(false);
    }
  };

  const handleFlashToggle = async () => {
    if (isCapturingPhoto) return;
    await toggleFlashSetting();
  };

  const handleSubmitPhoto = async () => {
    let base64String = photoPath;

    if (!isBarCodeScanner && photoPath) {
      base64String = await compressImageAndConvertToBase64(photoPath);
    }

    getImagePathOrBarcodeResult(base64String ?? '');
    navigation.goBack();
  };

  const codeScanner: CodeScanner = useCodeScanner({
    codeTypes: supportedCodeTypes,
    onCodeScanned: codes => {
      for (const code of codes) {
        setIsScanning(true);

        if (code.value) {
          const normalized = normalizeBarcodeValue(code.value, code.type);
          getImagePathOrBarcodeResult(normalized);
          setIsScanning(false);
          navigation.goBack();
          break; // Handle first valid code when multiple codes visible
        }
      }
    },
  });

  const onCameraRuntimeError = (error: CameraRuntimeError) => {
    Alert.alert('Error!', error.message);
  };

  const handleDevMode = useCallback(async () => {
    if (__DEV__) {
      if (isBarCodeScanner) {
        getImagePathOrBarcodeResult('1234567890');
      } else {
        const base64Image = await getRandomImgFile();
        getImagePathOrBarcodeResult(base64Image);
      }
      navigation.goBack();
    }
  }, [isBarCodeScanner, navigation, getImagePathOrBarcodeResult]);

  useEffect(() => {
    handleDevMode();
  }, [handleDevMode]);

  if (!device) {
    return (
      <View style={[center, container] as ViewStyle}>
        <Text>{en.loading}</Text>
      </View>
    );
  }

  const renderMessageContainer = () =>
    isReadyToCapturePhoto &&
    isMessageVisible &&
    infoMessage && (
      <View style={styles.messageContainer}>
        {MessageIconComponent && (
          <View style={styles.messageIconComponent}>
            <MessageIconComponent />
          </View>
        )}

        <View style={{ width: deviceWidth * 0.5 }}>
          <Text
            style={styles.messageText}
            numberOfLines={4}
            ellipsizeMode="tail">
            {isBarCodeScanner && isScanning ? en.scanning : infoMessage}
          </Text>
        </View>

        <IconButton
          onPress={() => setIsMessageVisible(false)}
          icon={<Cross />}
        />
      </View>
    );

  const renderCameraControls = () => (
    <View style={styles.buttonContainer}>
      <View style={container as ViewStyle}>
        {device?.hasFlash && (
          <IconButton
            onPress={handleFlashToggle}
            icon={flashSetting === 'auto' ? <AutoFlash /> : <Flash />}
            style={flashSetting !== 'off' ? styles.flashButtonOn : {}}
          />
        )}
      </View>

      <View style={[container, center] as ViewStyle}>
        {!isBarCodeScanner && (
          <IconButton
            onPress={handleCapturePhoto}
            icon={<CaptureButton />}
            disableInitialStyling
          />
        )}
      </View>

      <View style={container as ViewStyle} />
    </View>
  );

  const bottomPadding =
    insets.bottom === 0
      ? styles.buttonContainer.padding
      : Platform.select({
          ios: insets.bottom,
          android: insets.bottom + styles.buttonContainer.padding,
        });

  const renderPhotoPreview = () =>
    photoPath && (
      <>
        <View style={styles.cameraWrapper}>
          <Image
            source={{ uri: 'file://' + photoPath }}
            style={StyleSheet.absoluteFill}
            resizeMode="contain"
          />
        </View>

        <View style={[styles.photoOptionsContainer, { bottom: bottomPadding }]}>
          <FilledButton
            id={'CameraViewScreen.FilledButton.retake'}
            title={en.retake}
            onClick={() => setPhotoPath(null)}
            color="secondary"
            style={[primarySolidButton, { width: deviceWidth * 0.4 }]}
            containerStyle={row}
            textColor={Colors.black}
            isDisabled={false}
            isLoading={false}
          />
          <FilledButton
            id={'CameraViewScreen.FilledButton.submit'}
            title={en.submit}
            onClick={handleSubmitPhoto}
            color="primary"
            style={[primarySolidButton, { width: deviceWidth * 0.4 }]}
            containerStyle={row}
            isDisabled={false}
            isLoading={false}
          />
        </View>
      </>
    );

  return (
    <SafeAreaView style={styles.safeArea}>
      <TransparentHeader title={title} onBackPress={handleBackPress} />
      <>
        {!photoPath && (
          <View style={styles.cameraWrapper}>
            <Camera
              photo
              isActive={isActive}
              ref={camera}
              device={device}
              style={StyleSheet.absoluteFill}
              onInitialized={() => setIsCameraReady(true)}
              codeScanner={isBarCodeScanner ? codeScanner : undefined}
              onError={onCameraRuntimeError}
              resizeMode="contain"
              // Android-specific optimizations
              androidPreviewViewType="surface-view"
              enableFpsGraph={false}
              enableDepthData={false}
              enablePortraitEffectsMatteDelivery={false}
              {...(Platform.OS === 'android' && {
                format: undefined,
                fps: undefined,
              })}
            />
            {isReadyToCapturePhoto && (
              <View style={[container, center] as ViewStyle}>
                {isBarCodeScanner && <BarcodeScannerOverlay />}
                {renderMessageContainer()}
              </View>
            )}
          </View>
        )}
      </>

      {isReadyToCapturePhoto && (
        <View style={[container, center] as ViewStyle}>
          {renderCameraControls()}
        </View>
      )}

      {renderPhotoPreview()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    ...container,
    backgroundColor: 'black',
  },
  buttonContainer: {
    padding: 20,
    bottom: 0,
    position: 'absolute',
    flexDirection: 'row',
    alignItems: 'center',
  },
  messageContainer: {
    position: 'absolute',
    bottom: '12%',
    width: deviceWidth * 0.9,
    backgroundColor: colors.transWhite20,
    borderRadius: 10,
    paddingHorizontal: 10,
    paddingVertical: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  } as ViewStyle,
  messageIconComponent: {
    paddingHorizontal: 8,
  },
  messageText: {
    textAlign: 'left',
    lineHeight: 24,
    fontSize: 18,
    color: colors.white,
  },
  photoOptionsContainer: {
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    paddingHorizontal: 20,
  },
  flashButtonOn: {
    backgroundColor: colors.transWhite70,
  },
  cameraWrapper: {
    width: deviceWidth,
    height: previewHeight,
    alignSelf: 'center',
    marginTop: headerHeight / 2,
    backgroundColor: colors.black,
  },
});

export default CameraViewScreen;
